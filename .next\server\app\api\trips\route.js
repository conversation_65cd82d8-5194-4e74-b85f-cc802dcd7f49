"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/trips/route";
exports.ids = ["app/api/trips/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftrips%2Froute&page=%2Fapi%2Ftrips%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrips%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftrips%2Froute&page=%2Fapi%2Ftrips%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrips%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_trips_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/trips/route.ts */ \"(rsc)/./app/api/trips/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/trips/route\",\n        pathname: \"/api/trips\",\n        filename: \"route\",\n        bundlePath: \"app/api/trips/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\trips\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_trips_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/trips/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftrips%2Froute&page=%2Fapi%2Ftrips%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrips%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/trips/route.ts":
/*!********************************!*\
  !*** ./app/api/trips/route.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\n// GET /api/trips - Get all trips with filtering and pagination\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const destination = searchParams.get(\"destination\");\n        const difficulty = searchParams.get(\"difficulty\");\n        const minPrice = searchParams.get(\"minPrice\");\n        const maxPrice = searchParams.get(\"maxPrice\");\n        const minDuration = searchParams.get(\"minDuration\");\n        const maxDuration = searchParams.get(\"maxDuration\");\n        const dateFrom = searchParams.get(\"dateFrom\");\n        const dateTo = searchParams.get(\"dateTo\");\n        const search = searchParams.get(\"search\");\n        const featured = searchParams.get(\"featured\") === \"true\";\n        const includeInactive = searchParams.get(\"includeInactive\") === \"true\";\n        // Build query\n        let query = supabase.from(\"trips\").select(`\n        *,\n        trip_images(*)\n      `, {\n            count: \"exact\"\n        });\n        // Apply filters\n        if (!includeInactive) {\n            query = query.eq(\"is_active\", true);\n        }\n        if (featured) {\n            query = query.eq(\"is_featured\", true);\n        }\n        if (destination) {\n            query = query.ilike(\"destination\", `%${destination}%`);\n        }\n        if (difficulty) {\n            query = query.eq(\"difficulty\", difficulty);\n        }\n        if (minPrice) {\n            query = query.gte(\"price_per_person\", parseFloat(minPrice));\n        }\n        if (maxPrice) {\n            query = query.lte(\"price_per_person\", parseFloat(maxPrice));\n        }\n        if (minDuration) {\n            query = query.gte(\"duration_days\", parseInt(minDuration));\n        }\n        if (maxDuration) {\n            query = query.lte(\"duration_days\", parseInt(maxDuration));\n        }\n        if (dateFrom) {\n            query = query.gte(\"available_from\", dateFrom);\n        }\n        if (dateTo) {\n            query = query.lte(\"available_to\", dateTo);\n        }\n        if (search) {\n            query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%,destination.ilike.%${search}%`);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.range(from, to);\n        // Order by featured first, then by created_at\n        query = query.order(\"is_featured\", {\n            ascending: false\n        }).order(\"created_at\", {\n            ascending: false\n        });\n        const { data: trips, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching trips:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch trips\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: trips,\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/trips:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/trips - Create a new trip (Admin only)\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.verifySession)(\"admin\");\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Validate required fields\n        const requiredFields = [\n            \"title\",\n            \"slug\",\n            \"destination\",\n            \"duration_days\",\n            \"max_participants\",\n            \"price_per_person\",\n            \"difficulty\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if slug is unique\n        const { data: existingTrip } = await supabase.from(\"trips\").select(\"id\").eq(\"slug\", body.slug).single();\n        if (existingTrip) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Trip with this slug already exists\"\n            }, {\n                status: 400\n            });\n        }\n        // Create trip\n        const { data: trip, error } = await supabase.from(\"trips\").insert({\n            ...body,\n            min_participants: body.min_participants || 1,\n            is_active: body.is_active ?? true,\n            is_featured: body.is_featured ?? false\n        }).select().single();\n        if (error) {\n            console.error(\"Error creating trip:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create trip\"\n            }, {\n                status: 500\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: trip,\n            message: \"Trip created successfully\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/trips:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/trips/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\" || 0;\n// Server-side client for API routes, Server Components, and Server Actions\nconst createServerSupabase = ()=>{\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Server-side session verification function\nconst verifySession = async (requiredRole)=>{\n    try {\n        const supabase = createServerSupabase();\n        const { data: { session }, error } = await supabase.auth.getSession();\n        if (error) {\n            console.error(\"Error getting session:\", error);\n            return null;\n        }\n        if (!session) {\n            return null;\n        }\n        // If a specific role is required, check user role\n        if (requiredRole) {\n            const { data: user, error: userError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n            if (userError) {\n                console.error(\"Error getting user role:\", userError);\n                return null;\n            }\n            if (!user || user.role !== requiredRole) {\n                return null;\n            }\n        }\n        return session;\n    } catch (error) {\n        console.error(\"Error verifying session:\", error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftrips%2Froute&page=%2Fapi%2Ftrips%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftrips%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();