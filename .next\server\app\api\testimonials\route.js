"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/testimonials/route";
exports.ids = ["app/api/testimonials/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_testimonials_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/testimonials/route.ts */ \"(rsc)/./app/api/testimonials/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/testimonials/route\",\n        pathname: \"/api/testimonials\",\n        filename: \"route\",\n        bundlePath: \"app/api/testimonials/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\testimonials\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_testimonials_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/testimonials/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/testimonials/route.ts":
/*!***************************************!*\
  !*** ./app/api/testimonials/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\n// GET /api/testimonials - Get testimonials with filtering\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Parse query parameters\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n        const featured = searchParams.get(\"featured\") === \"true\";\n        const tripId = searchParams.get(\"tripId\");\n        const includeUnapproved = searchParams.get(\"includeUnapproved\") === \"true\";\n        // Check if user is admin for unapproved testimonials\n        let isAdmin = false;\n        if (includeUnapproved) {\n            const session = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.verifySession)();\n            if (session) {\n                const { data: user } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n                isAdmin = user?.role === \"admin\";\n            }\n        }\n        // Build query\n        let query = supabase.from(\"testimonials\").select(`\n        *,\n        user:users(id, full_name),\n        trip:trips(id, title, destination)\n      `, {\n            count: \"exact\"\n        });\n        // Apply filters\n        if (!includeUnapproved || !isAdmin) {\n            query = query.eq(\"is_approved\", true);\n        }\n        if (featured) {\n            query = query.eq(\"is_featured\", true);\n        }\n        if (tripId) {\n            query = query.eq(\"trip_id\", tripId);\n        }\n        // Apply pagination\n        const from = (page - 1) * limit;\n        const to = from + limit - 1;\n        query = query.range(from, to);\n        // Order by featured first, then by created_at\n        query = query.order(\"is_featured\", {\n            ascending: false\n        }).order(\"created_at\", {\n            ascending: false\n        });\n        const { data: testimonials, error, count } = await query;\n        if (error) {\n            console.error(\"Error fetching testimonials:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to fetch testimonials\"\n            }, {\n                status: 500\n            });\n        }\n        const totalPages = Math.ceil((count || 0) / limit);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: testimonials,\n            pagination: {\n                page,\n                limit,\n                total: count || 0,\n                totalPages\n            }\n        });\n    } catch (error) {\n        console.error(\"Error in GET /api/testimonials:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/testimonials - Create a new testimonial\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        // Validate required fields\n        const requiredFields = [\n            \"name\",\n            \"rating\",\n            \"content\"\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate rating\n        if (body.rating < 1 || body.rating > 5) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Rating must be between 1 and 5\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if user is authenticated (optional)\n        const session = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.verifySession)();\n        let userId = null;\n        let isAdmin = false;\n        if (session) {\n            userId = session.user.id;\n            const { data: user } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n            isAdmin = user?.role === \"admin\";\n        }\n        // Validate trip exists if trip_id is provided\n        if (body.trip_id) {\n            const { data: trip, error: tripError } = await supabase.from(\"trips\").select(\"id\").eq(\"id\", body.trip_id).single();\n            if (tripError || !trip) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Trip not found\"\n                }, {\n                    status: 404\n                });\n            }\n        }\n        // Create testimonial\n        const { data: testimonial, error } = await supabase.from(\"testimonials\").insert({\n            ...body,\n            user_id: userId,\n            is_approved: isAdmin,\n            is_featured: false\n        }).select(`\n        *,\n        user:users(id, full_name),\n        trip:trips(id, title, destination)\n      `).single();\n        if (error) {\n            console.error(\"Error creating testimonial:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create testimonial\"\n            }, {\n                status: 500\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            data: testimonial,\n            message: isAdmin ? \"Testimonial created and approved\" : \"Testimonial submitted for review\"\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"Error in POST /api/testimonials:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/testimonials/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\" || 0;\n// Server-side client for API routes, Server Components, and Server Actions\nconst createServerSupabase = ()=>{\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Server-side session verification function\nconst verifySession = async (requiredRole)=>{\n    try {\n        const supabase = createServerSupabase();\n        const { data: { session }, error } = await supabase.auth.getSession();\n        if (error) {\n            console.error(\"Error getting session:\", error);\n            return null;\n        }\n        if (!session) {\n            return null;\n        }\n        // If a specific role is required, check user role\n        if (requiredRole) {\n            const { data: user, error: userError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n            if (userError) {\n                console.error(\"Error getting user role:\", userError);\n                return null;\n            }\n            if (!user || user.role !== requiredRole) {\n                return null;\n            }\n        }\n        return session;\n    } catch (error) {\n        console.error(\"Error verifying session:\", error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc3VwYWJhc2Utc2VydmVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBQ1o7QUFHdkMsdURBQXVEO0FBQ3ZELE1BQU1FLGNBQWNDLDBDQUFvQyxJQUFJO0FBQzVELE1BQU1HLGtCQUFrQkgsa05BQXlDLElBQUk7QUFFckUsMkVBQTJFO0FBQ3BFLE1BQU1LLHVCQUF1QjtJQUNsQyxNQUFNQyxjQUFjUixxREFBT0E7SUFFM0IsT0FBT0QsaUVBQWtCQSxDQUN2QkUsYUFDQUksaUJBQ0E7UUFDRUwsU0FBUztZQUNQUztnQkFDRSxPQUFPRCxZQUFZQyxNQUFNO1lBQzNCO1lBQ0FDLFFBQU9DLFlBQVk7Z0JBQ2pCLElBQUk7b0JBQ0ZBLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FDNUNQLFlBQVlRLEdBQUcsQ0FBQ0gsTUFBTUMsT0FBT0M7Z0JBRWpDLEVBQUUsT0FBTTtnQkFDTiwwREFBMEQ7Z0JBQzFELHdEQUF3RDtnQkFDeEQsaUJBQWlCO2dCQUNuQjtZQUNGO1FBQ0Y7SUFDRjtBQUVKLEVBQUU7QUFFRiw0Q0FBNEM7QUFDckMsTUFBTUUsZ0JBQWdCLE9BQU9DO0lBQ2xDLElBQUk7UUFDRixNQUFNQyxXQUFXWjtRQUNqQixNQUFNLEVBQUVhLE1BQU0sRUFBRUMsT0FBTyxFQUFFLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1ILFNBQVNJLElBQUksQ0FBQ0MsVUFBVTtRQUVuRSxJQUFJRixPQUFPO1lBQ1RHLFFBQVFILEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDLE9BQU87UUFDVDtRQUVBLElBQUksQ0FBQ0QsU0FBUztZQUNaLE9BQU87UUFDVDtRQUVBLGtEQUFrRDtRQUNsRCxJQUFJSCxjQUFjO1lBQ2hCLE1BQU0sRUFBRUUsTUFBTU0sSUFBSSxFQUFFSixPQUFPSyxTQUFTLEVBQUUsR0FBRyxNQUFNUixTQUM1Q1MsSUFBSSxDQUFDLFNBQ0xDLE1BQU0sQ0FBQyxRQUNQQyxFQUFFLENBQUMsTUFBTVQsUUFBUUssSUFBSSxDQUFDSyxFQUFFLEVBQ3hCQyxNQUFNO1lBRVQsSUFBSUwsV0FBVztnQkFDYkYsUUFBUUgsS0FBSyxDQUFDLDRCQUE0Qks7Z0JBQzFDLE9BQU87WUFDVDtZQUVBLElBQUksQ0FBQ0QsUUFBUUEsS0FBS08sSUFBSSxLQUFLZixjQUFjO2dCQUN2QyxPQUFPO1lBQ1Q7UUFDRjtRQUVBLE9BQU9HO0lBQ1QsRUFBRSxPQUFPQyxPQUFPO1FBQ2RHLFFBQVFILEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU87SUFDVDtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3NpdGl2ZTctdG91cmlzbS13ZWJzaXRlLy4vbGliL3N1cGFiYXNlLXNlcnZlci50cz8wOTRmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNlcnZlckNsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9zc3InO1xuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycyc7XG5pbXBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnQC90eXBlcy9zdXBhYmFzZSc7XG5cbi8vIEVudmlyb25tZW50IHZhcmlhYmxlcyB3aXRoIGZhbGxiYWNrcyBmb3IgZGV2ZWxvcG1lbnRcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIHx8ICdodHRwczovL3BsYWNlaG9sZGVyLnN1cGFiYXNlLmNvJztcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIHx8ICdwbGFjZWhvbGRlci1hbm9uLWtleSc7XG5cbi8vIFNlcnZlci1zaWRlIGNsaWVudCBmb3IgQVBJIHJvdXRlcywgU2VydmVyIENvbXBvbmVudHMsIGFuZCBTZXJ2ZXIgQWN0aW9uc1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVNlcnZlclN1cGFiYXNlID0gKCkgPT4ge1xuICBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcblxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50PERhdGFiYXNlPihcbiAgICBzdXBhYmFzZVVybCxcbiAgICBzdXBhYmFzZUFub25LZXksXG4gICAge1xuICAgICAgY29va2llczoge1xuICAgICAgICBnZXRBbGwoKSB7XG4gICAgICAgICAgcmV0dXJuIGNvb2tpZVN0b3JlLmdldEFsbCgpO1xuICAgICAgICB9LFxuICAgICAgICBzZXRBbGwoY29va2llc1RvU2V0KSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvb2tpZXNUb1NldC5mb3JFYWNoKCh7IG5hbWUsIHZhbHVlLCBvcHRpb25zIH0pID0+XG4gICAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldChuYW1lLCB2YWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICAvLyBUaGUgYHNldEFsbGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXG4gICAgICAgICAgICAvLyBUaGlzIGNhbiBiZSBpZ25vcmVkIGlmIHlvdSBoYXZlIG1pZGRsZXdhcmUgcmVmcmVzaGluZ1xuICAgICAgICAgICAgLy8gdXNlciBzZXNzaW9ucy5cbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH1cbiAgKTtcbn07XG5cbi8vIFNlcnZlci1zaWRlIHNlc3Npb24gdmVyaWZpY2F0aW9uIGZ1bmN0aW9uXG5leHBvcnQgY29uc3QgdmVyaWZ5U2Vzc2lvbiA9IGFzeW5jIChyZXF1aXJlZFJvbGU/OiAnYWRtaW4nIHwgJ2N1c3RvbWVyJykgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlU2VydmVyU3VwYWJhc2UoKTtcbiAgICBjb25zdCB7IGRhdGE6IHsgc2Vzc2lvbiB9LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgc2Vzc2lvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIC8vIElmIGEgc3BlY2lmaWMgcm9sZSBpcyByZXF1aXJlZCwgY2hlY2sgdXNlciByb2xlXG4gICAgaWYgKHJlcXVpcmVkUm9sZSkge1xuICAgICAgY29uc3QgeyBkYXRhOiB1c2VyLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcnMnKVxuICAgICAgICAuc2VsZWN0KCdyb2xlJylcbiAgICAgICAgLmVxKCdpZCcsIHNlc3Npb24udXNlci5pZClcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAodXNlckVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciByb2xlOicsIHVzZXJFcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXVzZXIgfHwgdXNlci5yb2xlICE9PSByZXF1aXJlZFJvbGUpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHNlc3Npb247XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdmVyaWZ5aW5nIHNlc3Npb246JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZVNlcnZlckNsaWVudCIsImNvb2tpZXMiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUFub25LZXkiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImNyZWF0ZVNlcnZlclN1cGFiYXNlIiwiY29va2llU3RvcmUiLCJnZXRBbGwiLCJzZXRBbGwiLCJjb29raWVzVG9TZXQiLCJmb3JFYWNoIiwibmFtZSIsInZhbHVlIiwib3B0aW9ucyIsInNldCIsInZlcmlmeVNlc3Npb24iLCJyZXF1aXJlZFJvbGUiLCJzdXBhYmFzZSIsImRhdGEiLCJzZXNzaW9uIiwiZXJyb3IiLCJhdXRoIiwiZ2V0U2Vzc2lvbiIsImNvbnNvbGUiLCJ1c2VyIiwidXNlckVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwiaWQiLCJzaW5nbGUiLCJyb2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftestimonials%2Froute&page=%2Fapi%2Ftestimonials%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftestimonials%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();